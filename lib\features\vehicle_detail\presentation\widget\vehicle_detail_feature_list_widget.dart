import '../../../../core/import/ui.dart';
import 'vehicle_detail_feature_detail_widget.dart';

class Feature {
  Feature({
    required this.img,
    required this.title,
    required this.description,
  });

  final Widget img;
  final String title;
  final String description;
}

class VehicleDetailFeaturesWidget extends StatelessWidget {
  const VehicleDetailFeaturesWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final features = [
      Feature(
        img: Assets.svg.star.svg(),
        title: 'Capacity',
        description: '1 Seats',
      ),
      Feature(
        img: Assets.svg.star.svg(),
        title: 'Capacity',
        description: '2 Seats',
      ),
      Feature(
        img: Assets.svg.star.svg(),
        title: 'Capacity',
        description: '3 Seats',
      ),
      Feature(
        img: Assets.svg.star.svg(),
        title: 'Capacity',
        description: '4 Seats',
      ),
      Feature(
        img: Assets.svg.star.svg(),
        title: 'Capacity',
        description: '5 Seats',
      ),
      Feature(
        img: Assets.svg.star.svg(),
        title: 'Capacity',
        description: '6 Seats',
      ),
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextWidget(
          text: 'Features',
          style: context.tM,
        ),
        18.verticalSpace,
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: (features.length / 3).ceil(),
          padding: EdgeInsets.zero,
          itemBuilder: (context, i) {
            final firstIndex = i * 3;
            final secondIndex = firstIndex + 1;
            final thirdIndex = secondIndex + 1;

            return Row(
              children: [
                Expanded(
                  child: VehicleDetailFeatureDetailWidget(
                    feature: features[firstIndex],
                  ),
                ),
                if (secondIndex < features.length) ...[
                  18.horizontalSpace,
                  Expanded(
                    child: VehicleDetailFeatureDetailWidget(
                      feature: features[secondIndex],
                    ),
                  ),
                ],
                if (thirdIndex < features.length) ...[
                  18.horizontalSpace,
                  Expanded(
                    child: VehicleDetailFeatureDetailWidget(
                      feature: features[thirdIndex],
                    ),
                  ),
                ],
              ],
            );
          },
        ),
      ],
    );
  }
}
