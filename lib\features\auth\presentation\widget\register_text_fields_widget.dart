import '../../../../core/import/ui.dart';
import '../../../../core/utils/validator.dart';
import '../viewmodel/auth_view_model.dart';

class RegisterTextFieldsWidget extends ConsumerWidget {
  const RegisterTextFieldsWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      children: [
        Row(
          children: [
            const Icon(
              Icons.person_outline,
              color: ColorConstant.gray500,
            ),
            15.horizontalSpace,
            Expanded(
              child: TextFormFieldWidget(
                onChanged: (name) {
                  ref.read(authViewModelProvider.notifier).updateName(name);
                },
                hintText: 'Full Name',
                validator: Validator.name,
              ),
            ),
          ],
        ),
        15.verticalSpace,
        Row(
          children: [
            const Icon(
              Icons.person_outline,
              color: ColorConstant.gray500,
            ),
            15.horizontalSpace,
            Expanded(
              child: TextFormFieldWidget(
                onChanged: (name) {
                  ref.read(authViewModelProvider.notifier).updateUsername(name);
                },
                hintText: 'Username',
                validator: Validator.username,
              ),
            ),
          ],
        ),
        15.verticalSpace,
        Row(
          children: [
            const Icon(
              Icons.phone_outlined,
              color: ColorConstant.gray500,
            ),
            15.horizontalSpace,
            Expanded(
              child: TextFormFieldWidget(
                onChanged: (phoneNum) {
                  ref.read(authViewModelProvider.notifier).updatePhoneNum(phoneNum);
                },
                hintText: 'Phone Number',
                validator: Validator.phoneNumber,
              ),
            ),
          ],
        ),
        15.verticalSpace,
        Row(
          children: [
            const Icon(
              Icons.alternate_email,
              color: ColorConstant.gray500,
            ),
            15.horizontalSpace,
            Expanded(
              child: Consumer(
                builder: (context, ref, child) {
                  return TextFormFieldWidget(
                    onChanged: (email) {
                      ref.read(authViewModelProvider.notifier).updateEmail(email);
                    },
                    hintText: 'Email ID',
                    validator: Validator.email,
                  );
                },
              ),
            ),
          ],
        ),
        15.verticalSpace,
        Row(
          children: [
            const Icon(
              Icons.lock_outline,
              color: ColorConstant.gray500,
            ),
            15.horizontalSpace,
            Expanded(
              child: Consumer(
                builder: (context, ref, child) {
                  final state = ref.watch(authViewModelProvider);

                  return TextFormFieldWidget(
                    onChanged: (pw) {
                      ref.read(authViewModelProvider.notifier).updatePW(pw);
                    },
                    hintText: 'Password',
                    obscureText: state.isPwObscure,
                    suffixIcon: GestureDetector(
                      onTap: () {
                        ref.read(authViewModelProvider.notifier).togglePWObscurity();
                      },
                      child: Icon(
                        state.isPwObscure ? Icons.visibility : Icons.visibility_off,
                        color: ColorConstant.gray500,
                      ),
                    ),
                    validator: Validator.password,
                  );
                },
              ),
            ),
          ],
        ),
      ],
    );
  }
}
