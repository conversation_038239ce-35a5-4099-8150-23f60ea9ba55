import 'package:dartz/dartz.dart';

import '../../../../core/import/ui.dart';
import '../../../../core/utils/typedef.dart';
import '../../domain/entity/auth_entity.dart';
import '../../domain/repository/auth_repository.dart';
import '../data_source/auth_data_source_impl.dart';
import '../model/request/register_req_model.dart';

final authRepoIMPLProvider = Provider<AuthRepoIMPL>(
  (ref) => AuthRepoIMPL(ref.read(authRemoteDataSourceProvider)),
);

class AuthRepoIMPL implements IAuthRepository {
  AuthRepoIMPL(this._authDataSourceIMPL);

  final AuthDataSourceIMPL _authDataSourceIMPL;

  @override
  REQUESTHANDLER<AuthEntity> login(String username, String pw) async {
    final data = await _authDataSourceIMPL.login(username, pw);

    return data.fold(
      (failure) => Left(failure),
      (success) => Right(success.toEntity()),
    );
  }

  @override
  REQUESTHANDLER<AuthEntity> register(RegisterReqModel regReqModel) async {
    final data = await _authDataSourceIMPL.register(regReqModel);

    return data.fold(
      (failure) => Left(failure),
      (success) => Right(success.toEntity()),
    );
  }
}
