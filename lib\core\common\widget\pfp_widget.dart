import '../../import/ui.dart';

class PfpWidget extends StatelessWidget {
  const PfpWidget({
    super.key,
    this.radius,
    required this.imgPath,
  });

  final double? radius;
  final String imgPath;

  @override
  Widget build(BuildContext context) {
    return CircleAvatar(
      radius: radius ?? 20.r,
      backgroundColor: ColorConstant.gray400,
      backgroundImage: AssetImage(imgPath),
    );
  }
}
