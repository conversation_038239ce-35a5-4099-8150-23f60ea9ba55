import '../../../../core/common/widget/header_widget.dart';
import '../../../../core/import/ui.dart';
import '../widget/booking_detail_timeline_widget.dart';

class BookingDetailScreen extends StatelessWidget {
  const BookingDetailScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Safe<PERSON><PERSON>(
        bottom: false,
        child: Column(
          children: [
            const HeaderWidget(title: 'Booking Details'),
            const BookingDetailTimelineWidget(),
            Expanded(
              child: PageView.builder(
                itemCount: 3,
                itemBuilder: (context, i) {
                  return const TextWidget(text: 'AA');
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
