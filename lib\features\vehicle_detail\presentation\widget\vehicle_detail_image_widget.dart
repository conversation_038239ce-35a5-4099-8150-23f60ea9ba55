import '../../../../core/common/widget/circle_svg_widget.dart';
import '../../../../core/import/ui.dart';

class VehicleDetailImageWidget extends StatelessWidget {
  const VehicleDetailImageWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Assets.img.bugatti.image(),
        Positioned(
          top: 10.h,
          right: 10.w,
          child: CircleSvgWidget(
            circleColor: ColorConstant.white,
            padding: const EdgeInsets.all(10),
            svg: Assets.svg.heart.svg(
              colorFilter: const ColorFilter.mode(
                ColorConstant.primary,
                BlendMode.srcIn,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
