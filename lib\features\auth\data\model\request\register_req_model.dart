import 'package:json_annotation/json_annotation.dart';

part 'register_req_model.g.dart';

@JsonSerializable()
class RegisterReqModel {
  RegisterReqModel({
    required this.name,
    required this.username,
    required this.email,
    required this.password,
    required this.phoneNumber,
    this.role = '1',
  });

  final String name;
  final String username;
  final String email;
  final String password;
  final String phoneNumber;
  final String? role;

  factory RegisterReqModel.fromJson(Map<String, dynamic> json) => _$RegisterReqModelFromJson(json);

  Map<String, dynamic> toJson() => _$RegisterReqModelToJson(this);
}
