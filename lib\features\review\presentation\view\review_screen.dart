import '../../../../core/common/widget/header_widget.dart';
import '../../../../core/import/ui.dart';
import '../widget/review_list_widget.dart';

class Review {
  Review({
    required this.pfpPath,
    required this.name,
    required this.daysAgo,
    required this.rating,
    this.review,
  });

  final String pfpPath;
  final String name;
  final int daysAgo;
  final double rating;
  final String? review;
}

class ReviewScreen extends StatelessWidget {
  const ReviewScreen({
    super.key,
    required this.reviews,
  });

  final List<Review> reviews;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Safe<PERSON>rea(
        bottom: false,
        child: SingleChildScrollView(
          child: Column(
            children: [
              const HeaderWidget(title: 'Reviews'),
              ReviewListWidget(reviews: reviews),
            ],
          ),
        ),
      ),
    );
  }
}
