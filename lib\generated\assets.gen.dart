/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart' as _svg;
import 'package:vector_graphics/vector_graphics.dart' as _vg;

class $AssetsFontGen {
  const $AssetsFontGen();
}

class $AssetsImgGen {
  const $AssetsImgGen();

  /// File path: assets/img/bugatti.jpg
  AssetGenImage get bugatti => const AssetGenImage('assets/img/bugatti.jpg');

  /// File path: assets/img/cat_pfp.jpg
  AssetGenImage get catPfp => const AssetGenImage('assets/img/cat_pfp.jpg');

  /// List of all assets
  List<AssetGenImage> get values => [bugatti, catPfp];
}

class $AssetsSvgGen {
  const $AssetsSvgGen();

  /// File path: assets/svg/back_arrow.svg
  SvgGenImage get backArrow => const SvgGenImage('assets/svg/back_arrow.svg');

  /// File path: assets/svg/bell.svg
  SvgGenImage get bell => const SvgGenImage('assets/svg/bell.svg');

  /// File path: assets/svg/car.svg
  SvgGenImage get car => const SvgGenImage('assets/svg/car.svg');

  /// File path: assets/svg/empty_star.svg
  SvgGenImage get emptyStar => const SvgGenImage('assets/svg/empty_star.svg');

  /// File path: assets/svg/filter.svg
  SvgGenImage get filter => const SvgGenImage('assets/svg/filter.svg');

  /// File path: assets/svg/forgot_password.svg
  SvgGenImage get forgotPassword =>
      const SvgGenImage('assets/svg/forgot_password.svg');

  /// File path: assets/svg/forward_arrow.svg
  SvgGenImage get forwardArrow =>
      const SvgGenImage('assets/svg/forward_arrow.svg');

  /// File path: assets/svg/heart.svg
  SvgGenImage get heart => const SvgGenImage('assets/svg/heart.svg');

  /// File path: assets/svg/location.svg
  SvgGenImage get location => const SvgGenImage('assets/svg/location.svg');

  /// File path: assets/svg/login.svg
  SvgGenImage get login => const SvgGenImage('assets/svg/login.svg');

  /// File path: assets/svg/motorcycle.svg
  SvgGenImage get motorcycle => const SvgGenImage('assets/svg/motorcycle.svg');

  /// File path: assets/svg/notice_icon.svg
  SvgGenImage get noticeIcon => const SvgGenImage('assets/svg/notice_icon.svg');

  /// File path: assets/svg/phone.svg
  SvgGenImage get phone => const SvgGenImage('assets/svg/phone.svg');

  /// File path: assets/svg/search.svg
  SvgGenImage get search => const SvgGenImage('assets/svg/search.svg');

  /// File path: assets/svg/sign_in.svg
  SvgGenImage get signIn => const SvgGenImage('assets/svg/sign_in.svg');

  /// File path: assets/svg/star.svg
  SvgGenImage get star => const SvgGenImage('assets/svg/star.svg');

  /// List of all assets
  List<SvgGenImage> get values => [
    backArrow,
    bell,
    car,
    emptyStar,
    filter,
    forgotPassword,
    forwardArrow,
    heart,
    location,
    login,
    motorcycle,
    noticeIcon,
    phone,
    search,
    signIn,
    star,
  ];
}

class Assets {
  const Assets._();

  static const $AssetsFontGen font = $AssetsFontGen();
  static const $AssetsImgGen img = $AssetsImgGen();
  static const $AssetsSvgGen svg = $AssetsSvgGen();
}

class AssetGenImage {
  const AssetGenImage(this._assetName, {this.size, this.flavors = const {}});

  final String _assetName;

  final Size? size;
  final Set<String> flavors;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.medium,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({AssetBundle? bundle, String? package}) {
    return AssetImage(_assetName, bundle: bundle, package: package);
  }

  String get path => _assetName;

  String get keyName => _assetName;
}

class SvgGenImage {
  const SvgGenImage(this._assetName, {this.size, this.flavors = const {}})
    : _isVecFormat = false;

  const SvgGenImage.vec(this._assetName, {this.size, this.flavors = const {}})
    : _isVecFormat = true;

  final String _assetName;
  final Size? size;
  final Set<String> flavors;
  final bool _isVecFormat;

  _svg.SvgPicture svg({
    Key? key,
    bool matchTextDirection = false,
    AssetBundle? bundle,
    String? package,
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    AlignmentGeometry alignment = Alignment.center,
    bool allowDrawingOutsideViewBox = false,
    WidgetBuilder? placeholderBuilder,
    String? semanticsLabel,
    bool excludeFromSemantics = false,
    _svg.SvgTheme? theme,
    ColorFilter? colorFilter,
    Clip clipBehavior = Clip.hardEdge,
    @deprecated Color? color,
    @deprecated BlendMode colorBlendMode = BlendMode.srcIn,
    @deprecated bool cacheColorFilter = false,
  }) {
    final _svg.BytesLoader loader;
    if (_isVecFormat) {
      loader = _vg.AssetBytesLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
      );
    } else {
      loader = _svg.SvgAssetLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
        theme: theme,
      );
    }
    return _svg.SvgPicture(
      loader,
      key: key,
      matchTextDirection: matchTextDirection,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      allowDrawingOutsideViewBox: allowDrawingOutsideViewBox,
      placeholderBuilder: placeholderBuilder,
      semanticsLabel: semanticsLabel,
      excludeFromSemantics: excludeFromSemantics,
      colorFilter:
          colorFilter ??
          (color == null ? null : ColorFilter.mode(color, colorBlendMode)),
      clipBehavior: clipBehavior,
      cacheColorFilter: cacheColorFilter,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
