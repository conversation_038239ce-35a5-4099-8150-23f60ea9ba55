import '../config/theme/app_theme.dart';
import 'import/ui.dart';

final navKey = GlobalKey<NavigatorState>();

class App extends StatelessWidget {
  const App({super.key});

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(375, 812),
      builder: (_, child) {
        return MaterialApp(
          navigatorKey: navKey,
          initialRoute: AppRoute.homeRoute,
          onGenerateRoute: AppRoute.getApplicationRoute,
          title: '<PERSON>aa<PERSON><PERSON>',
          theme: AppTheme.getApplicationTheme(),
        );
      },
    );
  }
}
