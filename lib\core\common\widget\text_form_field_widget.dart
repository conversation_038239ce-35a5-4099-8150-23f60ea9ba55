import '../../import/ui.dart';

class TextFormFieldWidget extends StatelessWidget {
  const TextFormFieldWidget({
    super.key,
    this.onChanged,
    this.controller,
    this.focusNode,
    this.keyboardType,
    this.obscureText = false,
    this.hintText,
    this.hintStyle,
    this.suffixIcon,
    this.prefixIcon,
    this.validator,
  });

  final Function(String)? onChanged;
  final TextEditingController? controller;
  final FocusNode? focusNode;
  final TextInputType? keyboardType;
  final bool obscureText;
  final String? hintText;
  final TextStyle? hintStyle;
  final Widget? suffixIcon;
  final Widget? prefixIcon;
  final String? Function(String?)? validator;

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      onChanged: onChanged,
      controller: controller,
      focusNode: focusNode,
      keyboardType: keyboardType,
      obscureText: obscureText,
      obscuringCharacter: '*',
      validator: validator,
      decoration: InputDecoration(
        filled: true,
        fillColor: ColorConstant.white,
        hintText: hintText,
        hintStyle: hintStyle ??
            context.lM.copyWith(
              color: ColorConstant.gray400,
            ),
        suffixIcon: suffixIcon,
        prefixIcon: prefixIcon,
        errorMaxLines: 3,
        errorStyle: context.lS.copyWith(
          color: ColorConstant.error,
          fontSize: 10.sp,
        ),
        enabledBorder: OutlineInputBorder(
          borderSide: BorderSide(
            color: ColorConstant.gray400,
            width: 1.w,
          ),
          borderRadius: BorderRadius.circular(10.r),
        ),
        focusedBorder: OutlineInputBorder(
          borderSide: BorderSide(
            color: ColorConstant.gray500,
            width: 1.5.w,
          ),
          borderRadius: BorderRadius.circular(10.r),
        ),
        errorBorder: OutlineInputBorder(
          borderSide: BorderSide(
            color: ColorConstant.error,
            width: 1.w,
          ),
          borderRadius: BorderRadius.circular(10.r),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderSide: BorderSide(
            color: ColorConstant.error,
            width: 1.5.w,
          ),
          borderRadius: BorderRadius.circular(10.r),
        ),
      ),
    );
  }
}
