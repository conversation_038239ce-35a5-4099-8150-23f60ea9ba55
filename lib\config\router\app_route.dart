import '../../core/import/ui.dart';
import '../../features/auth/presentation/view/forgot_password_screen.dart';
import '../../features/auth/presentation/view/login_screen.dart';
import '../../features/auth/presentation/view/register_screen.dart';
import '../../features/booking_detail/presentation/view/booking_detail_screen.dart';
import '../../features/home/<USER>/view/home_screen.dart';
import '../../features/home/<USER>/widget/home_body_widget.dart';
import '../../features/review/presentation/view/review_screen.dart';
import '../../features/splash/presentation/view/splash_screen.dart';
import '../../features/vehicle_detail/presentation/view/vehicle_detail_screen.dart';

class AppRoute {
  AppRoute._();

  static const splashRoute = '/splash';
  static const loginRoute = '/login';
  static const registerRoute = '/register';
  static const forgotPasswordRoute = '/forgotPassword';
  static const homeRoute = '/home';
  static const vehicleDetailRoute = '/vehicleDetail';
  static const reviewRoute = '/review';
  static const bookingDetailRoute = '/bookingDetail';

  static Route<dynamic> getApplicationRoute(RouteSettings settings) {
    final argument = settings.arguments;

    switch (settings.name) {
      case splashRoute:
        return MaterialPageRoute(
          builder: (_) => const SplashScreen(),
        );
      case loginRoute:
        return MaterialPageRoute(
          builder: (_) => const LoginScreen(),
        );
      case registerRoute:
        return MaterialPageRoute(
          builder: (_) => const RegisterScreen(),
        );
      case forgotPasswordRoute:
        return MaterialPageRoute(
          builder: (_) => const ForgotPasswordScreen(),
        );
      case homeRoute:
        return MaterialPageRoute(
          builder: (_) => const HomeScreen(),
        );
      case vehicleDetailRoute:
        return MaterialPageRoute(
          builder: (_) => VehicleDetailScreen(
            vehicle: argument! as Vehicle,
          ),
        );
      case reviewRoute:
        return MaterialPageRoute(
          builder: (_) => ReviewScreen(
            reviews: argument! as List<Review>,
          ),
        );
      case bookingDetailRoute:
        return MaterialPageRoute(
          builder: (_) => const BookingDetailScreen(),
        );

      default:
        return MaterialPageRoute(
          builder: (_) => const DefaultScreen(),
        );
    }
  }
}

class DefaultScreen extends StatelessWidget {
  const DefaultScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Assets.svg.noticeIcon.svg(
              height: 45.h,
              width: 45.h,
            ),
            16.verticalSpace,
            Text(
              'Failed to Navigate',
              style: TextStyle(
                fontSize: 18.sp,
                // height: 25.2.sp / 18.sp,
                fontWeight: FontWeight.w500,
                color: const Color(0xFF292929),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
