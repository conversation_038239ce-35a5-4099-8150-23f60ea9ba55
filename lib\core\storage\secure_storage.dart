import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

import '../failure/failure.dart';
import '../utils/is_token_expired.dart';
import '../utils/typedef.dart';

final secureStorageProvider = Provider((ref) => SecureStorage());

class SecureStorage {
  final FlutterSecureStorage _flutterSecureStorage = const FlutterSecureStorage();

  REQUESTHANDLER<String?> readToken(String key) async {
    try {
      String? token = await _flutterSecureStorage.read(key: key);

      if (token == null) return const Right(null);

      if (isTokenExpired(token)) {
        await deleteToken(token);

        return const Right(null);
      }

      return Right(token);
    } catch (e) {
      return Left(Failure(error: e.toString()));
    }
  }

  REQUESTHANDLER<bool> writeToken(String key, String? value) async {
    try {
      await _flutterSecureStorage.write(key: key, value: value);

      return const Right(true);
    } catch (e) {
      return Left(Failure(error: e.toString()));
    }
  }

  REQUESTHANDLER<bool> deleteToken(String key) async {
    try {
      await _flutterSecureStorage.delete(key: key);

      return const Right(true);
    } catch (e) {
      return Left(Failure(error: e.toString()));
    }
  }

  REQUESTHANDLER<bool> deleteAllTokens() async {
    try {
      await _flutterSecureStorage.deleteAll();

      return const Right(true);
    } catch (e) {
      return Left(Failure(error: e.toString()));
    }
  }
}
