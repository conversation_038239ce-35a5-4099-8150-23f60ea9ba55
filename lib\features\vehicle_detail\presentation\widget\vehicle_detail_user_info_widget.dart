import '../../../../core/common/widget/circle_svg_widget.dart';
import '../../../../core/common/widget/pfp_widget.dart';
import '../../../../core/import/ui.dart';

class VehicleDetailUserInfoWidget extends StatelessWidget {
  const VehicleDetailUserInfoWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            PfpWidget(radius: 25.r, imgPath: Assets.img.catPfp.path),
            18.horizontalSpace,
            TextWidget(
              text: '<PERSON>',
              style: context.tS,
            ),
          ],
        ),
        CircleSvgWidget(
          circleColor: ColorConstant.white,
          padding: const EdgeInsets.all(8),
          svg: Assets.svg.phone.svg(
            colorFilter: const ColorFilter.mode(
              ColorConstant.primary,
              BlendMode.srcIn,
            ),
          ),
        ),
      ],
    );
  }
}
