import '../../../../core/common/widget/circle_svg_widget.dart';
import '../../../../core/common/widget/pfp_widget.dart';
import '../../../../core/import/ui.dart';

class HomeHeaderWidget extends StatelessWidget {
  const HomeHeaderWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.w),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TextWidget(
                text: 'Gaadiyo',
                style: context.hL,
              ),
              Row(
                children: [
                  CircleSvgWidget(
                    padding: const EdgeInsets.all(10),
                    svg: Assets.svg.bell.svg(
                      colorFilter: const ColorFilter.mode(
                        ColorConstant.primary,
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                  15.horizontalSpace,
                  PfpWidget(imgPath: Assets.img.catPfp.path),
                ],
              ),
            ],
          ),
        ),
        20.verticalSpace,
        const Divider(),
      ],
    );
  }
}
