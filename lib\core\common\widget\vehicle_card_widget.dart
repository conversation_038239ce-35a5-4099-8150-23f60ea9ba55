import '../../../features/home/<USER>/widget/home_body_widget.dart';
import '../../import/ui.dart';
import 'button_widget.dart';
import 'circle_svg_widget.dart';

class VehicleCardWidget extends StatelessWidget {
  const VehicleCardWidget({
    super.key,
    required this.vehicle,
  });

  final Vehicle vehicle;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () async {
        await Navigator.pushNamed(
          context,
          AppRoute.vehicleDetailRoute,
          arguments: vehicle,
        );

        // await Navigator.pushNamed(context, AppRoute.reviewRoute);
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15.r),
          border: Border.all(
            color: ColorConstant.gray400,
            width: 1.w,
          ),
        ),
        margin: EdgeInsets.only(bottom: 18.h),
        child: Column(
          children: [
            Stack(
              children: [
                Hero(
                  tag: vehicle.name,
                  child: <PERSON><PERSON><PERSON><PERSON>(
                    height: 100.h,
                    width: double.infinity,
                    child: ClipRRect(
                      borderRadius: BorderRadius.vertical(top: Radius.circular(15.r)),
                      child: Assets.img.bugatti.image(
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                ),
                Positioned(
                  top: 10.h,
                  right: 10.w,
                  child: CircleSvgWidget(
                    circleColor: ColorConstant.white,
                    padding: const EdgeInsets.all(8),
                    svg: Assets.svg.heart.svg(
                      height: 12.h,
                      width: 12.w,
                      colorFilter: const ColorFilter.mode(
                        ColorConstant.primary,
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            Padding(
              padding: const EdgeInsets.all(10),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TextWidget(
                    text: vehicle.name,
                    style: context.tS,
                  ),
                  10.verticalSpace,
                  Row(
                    children: [
                      TextWidget(
                        text: vehicle.rating.toString(),
                        style: context.bS.copyWith(
                          color: ColorConstant.gray600,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      5.horizontalSpace,
                      Assets.svg.star.svg(),
                    ],
                  ),
                  5.verticalSpace,
                  Row(
                    children: [
                      Assets.svg.location.svg(
                        height: 15.h,
                        width: 15.w,
                      ),
                      5.horizontalSpace,
                      TextWidget(
                        text: vehicle.location,
                        style: context.bS.copyWith(
                          color: ColorConstant.gray600,
                        ),
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      Expanded(
                        child: TextWidget(
                          text: 'Rs. ${vehicle.pricePerDay}/Day',
                          style: context.bS,
                          maxLine: 2,
                        ),
                      ),
                      10.horizontalSpace,
                      Expanded(
                        child: ButtonWidget(
                          onPressed: () {},
                          text: 'Book',
                          buttonColor: ColorConstant.secondary,
                          textStyle: context.lS.copyWith(
                            color: ColorConstant.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
