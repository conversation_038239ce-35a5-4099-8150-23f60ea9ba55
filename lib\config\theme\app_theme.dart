import 'package:flutter/cupertino.dart';

import '../../core/import/ui.dart';
import '../constants/typography_constant.dart';

class AppTheme {
  AppTheme._();

  static ThemeData getApplicationTheme() {
    return ThemeData(
      primaryColor: ColorConstant.primary,
      cupertinoOverrideTheme: const NoDefaultCupertinoThemeData(
        primaryColor: ColorConstant.gray500,
      ),
      textSelectionTheme: const TextSelectionThemeData(
        cursorColor: ColorConstant.gray500,
        selectionColor: ColorConstant.gray400,
        selectionHandleColor: ColorConstant.gray500,
      ),
      scaffoldBackgroundColor: ColorConstant.bg,
      progressIndicatorTheme: const ProgressIndicatorThemeData(
        color: ColorConstant.primary,
      ),
      fontFamily: FontFamily.satoshi,
      textTheme: TypographyConstant.textTheme,
      splashFactory: NoSplash.splashFactory,
      iconTheme: const IconThemeData(
        color: ColorConstant.black,
      ),
      dividerTheme: const DividerThemeData(
        space: 0,
        color: ColorConstant.gray400,
      ),
    );
  }
}
