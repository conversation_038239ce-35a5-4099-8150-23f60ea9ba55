import '../../../../core/common/widget/vehicle_card_widget.dart';
import '../../../../core/import/ui.dart';

class Vehicle {
  Vehicle({
    required this.name,
    required this.rating,
    required this.location,
    required this.numOfSeats,
    required this.pricePerDay,
  });

  final String name;
  final double rating;
  final String location;
  final int numOfSeats;
  final int pricePerDay;
}

class HomeBodyWidget extends StatelessWidget {
  const HomeBodyWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final vehicles = [
      Vehicle(
        name: 'Bugatti Bolide 1',
        rating: 5.0,
        location: 'Washington DC',
        numOfSeats: 4,
        pricePerDay: 9000,
      ),
      Vehicle(
        name: 'Bugatti Bolide 2',
        rating: 5.0,
        location: 'Washington DC',
        numOfSeats: 4,
        pricePerDay: 9000,
      ),
      Vehicle(
        name: 'Bugatti Bolide 3',
        rating: 5.0,
        location: 'Washington DC',
        numOfSeats: 4,
        pricePerDay: 9000,
      ),
      Vehicle(
        name: 'Bugatti Bolide 4',
        rating: 5.0,
        location: 'Washington DC',
        numOfSeats: 4,
        pricePerDay: 9000,
      ),
      Vehicle(
        name: 'Bugatti Bolide 5',
        rating: 5.0,
        location: 'Washington DC',
        numOfSeats: 4,
        pricePerDay: 9000,
      ),
    ];

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: ColorConstant.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(30.r),
          topRight: Radius.circular(30.r),
        ),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TextWidget(
                text: 'For You',
                style: context.tM,
              ),
              TextWidget(
                text: 'View All',
                style: context.bS.copyWith(
                  color: ColorConstant.secondary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          18.verticalSpace,
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            padding: EdgeInsets.zero,
            itemCount: (vehicles.length / 2).ceil(),
            itemBuilder: (context, i) {
              final firstIndex = i * 2;
              final secondIndex = firstIndex + 1;

              return Row(
                children: [
                  Expanded(
                    child: VehicleCardWidget(
                      vehicle: vehicles[firstIndex],
                    ),
                  ),
                  if (secondIndex < vehicles.length) ...[
                    18.horizontalSpace,
                    Expanded(
                      child: VehicleCardWidget(
                        vehicle: vehicles[secondIndex],
                      ),
                    ),
                  ],
                ],
              );
            },
          ),
        ],
      ),
    );
  }
}
