import '../../../../core/common/widget/button_widget.dart';
import '../../../../core/import/ui.dart';

class ForgotPasswordScreen extends StatelessWidget {
  const ForgotPasswordScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: Scaffold(
        body: SafeArea(
          bottom: false,
          child: SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  GestureDetector(
                    onTap: () => context.popTimes(1),
                    child: const Icon(
                      Icons.arrow_back_ios,
                    ),
                  ),
                  5.verticalSpace,
                  Assets.svg.forgotPassword.svg(height: 0.3.sh),
                  TextWidget(
                    text: 'Forgot\nPassword?',
                    style: context.hL,
                    // fontSize: 30.sp,
                    // fontWeight: FontWeight.bold,
                  ),
                  10.verticalSpace,
                  TextWidget(
                    text: 'Dont worry! It happens. Please enter the address associated with your account.',
                    maxLine: 2,
                    style: context.tS,
                    // color: ColorConstant.gray500,
                    // fontSize: 13.sp,
                    // fontWeight: FontWeight.w600,
                  ),
                  50.verticalSpace,
                  Row(
                    children: [
                      const Icon(
                        Icons.alternate_email,
                        color: ColorConstant.gray500,
                      ),
                      15.horizontalSpace,
                      const Expanded(
                        child: TextFormFieldWidget(
                          hintText: 'Email ID / Phone Number',
                        ),
                      ),
                    ],
                  ),
                  50.verticalSpace,
                  ButtonWidget(
                    onPressed: () {},
                    text: 'Submit',
                    buttonHeight: 50.h,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
