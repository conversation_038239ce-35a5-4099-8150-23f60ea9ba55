import '../../../../core/common/widget/button_widget.dart';
import '../../../../core/import/ui.dart';
import '../../data/model/request/register_req_model.dart';
import '../viewmodel/auth_view_model.dart';
import '../widget/register_text_fields_widget.dart';

class RegisterScreen extends StatelessWidget {
  const RegisterScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final formKey = GlobalKey<FormState>();

    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Scaffold(
        body: SafeArea(
          bottom: false,
          child: Form(
            key: formKey,
            child: SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.only(left: 20.w, right: 20.w, bottom: 20.h),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    GestureDetector(
                      onTap: () => context.popTimes(1),
                      child: const Icon(
                        Icons.arrow_back_ios,
                      ),
                    ),
                    5.verticalSpace,
                    Assets.svg.signIn.svg(height: 0.3.sh),
                    TextWidget(
                      text: 'Sign Up',
                      style: context.hL,
                    ),
                    30.verticalSpace,
                    const RegisterTextFieldsWidget(),
                    30.verticalSpace,
                    Consumer(
                      builder: (context, ref, child) {
                        final isLoading = ref.watch(
                          authViewModelProvider.select((state) => state.isLoading),
                        );

                        return ButtonWidget(
                          onPressed: () async {
                            if (!formKey.currentState!.validate()) return;

                            final regReqModel = RegisterReqModel(
                              name: ref.read(authViewModelProvider).name ?? '',
                              username: ref.read(authViewModelProvider).username ?? '',
                              email: ref.read(authViewModelProvider).email ?? '',
                              password: ref.read(authViewModelProvider).password ?? '',
                              phoneNumber: ref.read(authViewModelProvider).phoneNumber ?? '',
                            );

                            await ref.read(authViewModelProvider.notifier).register(
                                  context,
                                  regReqModel,
                                );
                          },
                          text: 'Continue',
                          isLoading: isLoading,
                          buttonHeight: 50.h,
                        );
                      },
                    ),
                    50.verticalSpace,
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        TextWidget(
                          text: 'Joined us before?',
                          style: context.bM,
                        ),
                        5.horizontalSpace,
                        GestureDetector(
                          onTap: () => context.popTimes(1),
                          child: TextWidget(
                            text: 'Login',
                            style: context.bM.copyWith(
                              color: ColorConstant.secondary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
