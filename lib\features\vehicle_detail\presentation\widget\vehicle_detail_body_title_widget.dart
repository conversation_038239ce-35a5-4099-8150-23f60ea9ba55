import '../../../../core/import/ui.dart';
import '../../../home/<USER>/widget/home_body_widget.dart';

class VehicleDetailBodyTitleWidget extends StatelessWidget {
  const VehicleDetailBodyTitleWidget({
    super.key,
    required this.vehicle,
  });

  final Vehicle vehicle;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextWidget(
          text: vehicle.name,
          style: context.tM,
        ),
        5.verticalSpace,
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: TextWidget(
                text: 'A car with high specs.',
                style: context.bS.copyWith(color: ColorConstant.gray600),
                maxLine: 2,
              ),
            ),
            Column(
              children: [
                Row(
                  children: [
                    TextWidget(
                      text: vehicle.rating.toString(),
                      style: context.tS,
                    ),
                    5.horizontalSpace,
                    Assets.svg.star.svg(),
                  ],
                ),
                5.verticalSpace,
                TextWidget(
                  text: '(100+ Reviews)',
                  style: context.bS.copyWith(color: ColorConstant.gray600),
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }
}
