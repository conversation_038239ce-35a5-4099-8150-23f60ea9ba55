import '../../../../core/common/widget/pfp_widget.dart';
import '../../../../core/extension/int_extension.dart';
import '../../../../core/import/ui.dart';
import '../view/review_screen.dart';

class ReviewDetailWidget extends StatelessWidget {
  const ReviewDetailWidget({
    super.key,
    required this.review,
  });

  final Review review;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      child: Container(
        decoration: BoxDecoration(
          color: ColorConstant.white,
          borderRadius: BorderRadius.circular(15.r),
          border: Border.all(
            color: ColorConstant.gray400,
            width: 1.w,
          ),
        ),
        padding: const EdgeInsets.all(10),
        margin: EdgeInsets.only(bottom: 18.h),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Row(
                    children: [
                      PfpWidget(radius: 15.r, imgPath: Assets.img.catPfp.path),
                      5.horizontalSpace,
                      Expanded(
                        child: TextWidget(
                          text: review.name,
                          style: context.tS,
                        ),
                      ),
                    ],
                  ),
                ),
                TextWidget(
                  text: review.daysAgo.timeAgo,
                  style: context.bS.copyWith(color: ColorConstant.primary),
                ),
              ],
            ),
            18.verticalSpace,
            Row(
              children: List.generate(
                5,
                (i) {
                  return i < review.rating.floor() ? Assets.svg.star.svg() : Assets.svg.emptyStar.svg();
                },
              ),
            ),
            if (review.review != null && review.review!.isNotEmpty) ...[
              18.verticalSpace,
              TextWidget(
                text: review.review!,
                style: context.bS,
                overflow: null,
              ),
            ],
          ],
        ),
      ),
    );
  }
}
