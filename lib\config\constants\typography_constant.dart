import '../../core/import/ui.dart';

class TypographyConstant {
  TypographyConstant._();

  static final _displayLarge = TextStyle(
    fontSize: 64.sp,
    fontWeight: FontWeight.bold,
    color: ColorConstant.black,
  );
  static final _displayMedium = TextStyle(
    fontSize: 48.sp,
    fontWeight: FontWeight.bold,
    color: ColorConstant.black,
  );
  static final _displaySmall = TextStyle(
    fontSize: 32.sp,
    fontWeight: FontWeight.bold,
    color: ColorConstant.black,
  );

  static final _headlineLarge = TextStyle(
    fontSize: 28.sp,
    fontWeight: FontWeight.bold,
    color: ColorConstant.black,
  );
  static final _headlineMedium = TextStyle(
    fontSize: 24.sp,
    fontWeight: FontWeight.bold,
    color: ColorConstant.black,
  );
  static final _headlineSmall = TextStyle(
    fontSize: 20.sp,
    fontWeight: FontWeight.bold,
    color: ColorConstant.black,
  );

  static final _titleLarge = TextStyle(
    fontSize: 18.sp,
    fontWeight: FontWeight.w600,
    color: ColorConstant.black,
  );
  static final _titleMedium = TextStyle(
    fontSize: 16.sp,
    fontWeight: FontWeight.w600,
    color: ColorConstant.black,
  );
  static final _titleSmall = TextStyle(
    fontSize: 14.sp,
    fontWeight: FontWeight.w600,
    color: ColorConstant.black,
  );

  static final _bodyLarge = TextStyle(
    fontSize: 16.sp,
    fontWeight: FontWeight.normal,
    color: ColorConstant.black,
  );
  static final _bodyMedium = TextStyle(
    fontSize: 14.sp,
    fontWeight: FontWeight.normal,
    color: ColorConstant.black,
  );
  static final _bodySmall = TextStyle(
    fontSize: 12.sp,
    fontWeight: FontWeight.normal,
    color: ColorConstant.black,
  );

  static final _labelLarge = TextStyle(
    fontSize: 16.sp,
    fontWeight: FontWeight.w600,
    color: ColorConstant.black,
  );
  static final _labelMedium = TextStyle(
    fontSize: 14.sp,
    fontWeight: FontWeight.w600,
    color: ColorConstant.black,
  );
  static final _labelSmall = TextStyle(
    fontSize: 12.sp,
    fontWeight: FontWeight.w600,
    color: ColorConstant.black,
  );

  static TextTheme get textTheme {
    return TextTheme(
      displayLarge: _displayLarge,
      displayMedium: _displayMedium,
      displaySmall: _displaySmall,
      headlineLarge: _headlineLarge,
      headlineMedium: _headlineMedium,
      headlineSmall: _headlineSmall,
      titleLarge: _titleLarge,
      titleMedium: _titleMedium,
      titleSmall: _titleSmall,
      bodyLarge: _bodyLarge,
      bodyMedium: _bodyMedium,
      bodySmall: _bodySmall,
      labelLarge: _labelLarge,
      labelMedium: _labelMedium,
      labelSmall: _labelSmall,
    );
  }
}
