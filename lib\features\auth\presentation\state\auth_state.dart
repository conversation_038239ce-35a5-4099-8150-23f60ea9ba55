import 'package:freezed_annotation/freezed_annotation.dart';

import '../../domain/entity/auth_entity.dart';

part 'auth_state.freezed.dart';

@freezed
class AuthState with _$AuthState {
  const factory AuthState({
    @Default(false) bool isLoading,
    @Default(true) bool isPwObscure,
    String? name,
    String? username,
    String? email,
    String? phoneNumber,
    String? password,
    AuthEntity? authEntity,
    String? error,
  }) = _AuthState;

  factory AuthState.initial() => const AuthState();
}
