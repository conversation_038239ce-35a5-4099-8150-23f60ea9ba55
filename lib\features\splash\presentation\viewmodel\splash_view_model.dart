import '../../../../core/import/ui.dart';
import '../../../../core/storage/secure_storage.dart';
import '../../../../core/utils/is_token_expired.dart';
import '../../../../core/utils/print_in_debug.dart';
import '../state/splash_state.dart';

final splashViewModelProvider = StateNotifierProvider.autoDispose<SplashViewModel, SplashState>(
  (ref) => SplashViewModel(
    ref.read(secureStorageProvider),
  ),
);

class SplashViewModel extends StateNotifier<SplashState> {
  SplashViewModel(this.secureStorage) : super(SplashState.initial());

  final SecureStorage secureStorage;

  Future<void> init(BuildContext context) async {
    state = state.copyWith(isLoading: true);

    final token = await secureStorage.readToken('token');

    await Future.delayed(const Duration(seconds: 2));

    token.fold(
      (failure) async {
        state = state.copyWith(isLoading: false);

        printInDebug('FAILED TO READ TOKEN::: ${failure.error}');

        await navigateToLogin(context);
      },
      (token) async {
        state = state.copyWith(isLoading: false);

        if (token == null || isTokenExpired(token)) {
          printInDebug('NO TOKEN:::: $token');

          await navigateToLogin(context);

          return;
        }

        printInDebug('TOKEN:::: $token');

        await Navigator.pushNamedAndRemoveUntil(
          context,
          AppRoute.homeRoute,
          (_) => false,
        );
      },
    );
  }

  Future<void> navigateToLogin(BuildContext context) async {
    await Navigator.pushNamedAndRemoveUntil(
      context,
      AppRoute.loginRoute,
      (_) => false,
    );
  }
}
