import 'dart:developer';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';

import '../../config/constants/api_endpoint.dart';
import '../storage/secure_storage.dart';
import 'dio_error_interceptor.dart';

final httpServiceProvider = Provider<Dio>(
  (ref) => HttpService(
    Dio(),
    SecureStorage(),
  ).dio,
);

class HttpService {
  final Dio _dio;
  Dio get dio => _dio;

  final SecureStorage _secureStorage;
  SecureStorage get secureStorage => _secureStorage;

  String? accessToken;

  HttpService(this._dio, this._secureStorage) {
    _dio
      ..options.baseUrl = ApiEndpoint.baseUrl
      ..options.connectTimeout = ApiEndpoint.connectionTimeout
      ..options.receiveTimeout = ApiEndpoint.receiveTimeout
      ..options.headers = {
        HttpHeaders.acceptHeader: ContentType.json.mimeType,
        HttpHeaders.contentTypeHeader: ContentType.json.mimeType,
        HttpHeaders.authorizationHeader: 'Bearer ',
      }
      ..interceptors.add(DioErrorInterceptor())
      ..interceptors.add(
        PrettyDioLogger(
          requestHeader: true,
          requestBody: true,
          responseHeader: true,
          logPrint: (object) => log(object.toString(), name: 'GAADIYO'),
        ),
      )
      ..interceptors.add(
        InterceptorsWrapper(
          onRequest: _onRequestInterceptor,
          onResponse: (response, handler) => handler.next(response),
          onError: _onErrorInterceptor,
        ),
      );
  }

  Future<void> _onRequestInterceptor(RequestOptions options, RequestInterceptorHandler handler) async {
    // final token = await secureStorage.readToken('accessToken');
    // token.fold((failure) => null, (success) => accessToken = success);

    // if (accessToken != null && accessToken!.isNotEmpty) {
    //   options.headers[HttpHeaders.authorizationHeader] = 'Bearer $accessToken';
    // }

    handler.next(options);
  }

  Future<void> _onErrorInterceptor(DioException err, ErrorInterceptorHandler handler) async {
    // if (err.response?.statusCode == 401 && err.response?.data['message'] == 'Invalid access token' ||
    //     err.response?.data['message'] == 'No access token provided') {
    //   final token = await refreshAccessToken();
    //   String? newAccessToken;

    //   token.fold(
    //     (failure) => printInDebug(failure.error),
    //     (success) => newAccessToken = success.data['accessToken'],
    //   );

    //   if (newAccessToken != null) {
    //     err.requestOptions.headers['Authorization'] = 'Bearer $newAccessToken';
    //     handler.resolve(await dio.fetch(err.requestOptions));
    //     return;
    //   }
    // }

    handler.next(err);
  }
}
