import '../../../../core/import/ui.dart';
import '../viewmodel/auth_view_model.dart';

class LoginTextFieldsWidget extends StatelessWidget {
  const LoginTextFieldsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            const Icon(
              Icons.alternate_email,
              color: ColorConstant.gray600,
            ),
            15.horizontalSpace,
            Expanded(
              child: Consumer(
                builder: (context, ref, child) {
                  return TextFormFieldWidget(
                    onChanged: (email) {
                      ref.read(authViewModelProvider.notifier).updateEmail(email);
                    },
                    hintText: 'Email ID',
                  );
                },
              ),
            ),
          ],
        ),
        15.verticalSpace,
        Row(
          children: [
            const Icon(
              Icons.lock_outline,
              color: ColorConstant.gray600,
            ),
            15.horizontalSpace,
            Expanded(
              child: Consumer(
                builder: (context, ref, child) {
                  final isPwObscure = ref.watch(
                    authViewModelProvider.select((state) => state.isPwObscure),
                  );

                  return TextFormFieldWidget(
                    onChanged: (pw) {
                      ref.read(authViewModelProvider.notifier).updatePW(pw);
                    },
                    hintText: 'Password',
                    obscureText: isPwObscure,
                    suffixIcon: GestureDetector(
                      onTap: () {
                        ref.read(authViewModelProvider.notifier).togglePWObscurity();
                      },
                      child: Icon(
                        isPwObscure ? Icons.visibility : Icons.visibility_off,
                        color: ColorConstant.gray600,
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ],
    );
  }
}
