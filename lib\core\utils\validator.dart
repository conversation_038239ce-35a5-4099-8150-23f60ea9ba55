class Validator {
  Validator._();

  static String? username(String? value) {
    if (value == null || value.isEmpty) return 'Username is required';

    if (value.length < 3 || value.length > 20) return 'Username must be between 3 and 20 characters';

    if (!RegExp(r'^[a-zA-Z0-9._]+$').hasMatch(value)) {
      return 'Username can only contain letters, numbers, underscores, and periods';
    }

    return null;
  }

  static String? name(String? value) {
    if (value == null || value.isEmpty) return 'Full name is required';

    if (value.length < 2) return 'Full name must be at least 2 characters';

    return null;
  }

  static String? email(String? value) {
    if (value == null || value.isEmpty) return 'Email is required';

    final emailRegex = RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
    if (!emailRegex.hasMatch(value)) return 'Enter a valid email';

    return null;
  }

  static String? phoneNumber(String? value) {
    if (value == null || value.isEmpty) return 'Phone number is required';

    return null;
  }

  static String? password(String? pw) {
    if (pw == null || pw.isEmpty) return 'Password is required';

    final errors = <String>[];

    if (pw.length < 6) errors.add('at least 6 characters');
    if (!RegExp(r'[A-Z]').hasMatch(pw)) errors.add('one uppercase letter');
    if (!RegExp(r'[a-z]').hasMatch(pw)) errors.add('one lowercase letter');
    if (!RegExp(r'[0-9]').hasMatch(pw)) errors.add('one number');
    if (!RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(pw)) errors.add('one special character');

    if (errors.isNotEmpty) return 'Password must contain ${errors.join(', ')}';

    return null;
  }
}
