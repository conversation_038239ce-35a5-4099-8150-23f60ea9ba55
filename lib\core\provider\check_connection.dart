import 'dart:async';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../utils/print_in_debug.dart';

enum ConnectivityStatus { notDetermined, connected, disconnected }

final connectivityStatusProvider = StateNotifierProvider<ConnectivityStatusNotifier, ConnectivityStatus>(
  (ref) => ConnectivityStatusNotifier(),
);

class ConnectivityStatusNotifier extends StateNotifier<ConnectivityStatus> {
  ConnectivityStatusNotifier() : super(ConnectivityStatus.notDetermined) {
    _init();
  }

  late final StreamSubscription<List<ConnectivityResult>> _streamSubscription;

  void _init() {
    _streamSubscription = Connectivity().onConnectivityChanged.listen(
      (results) {
        for (ConnectivityResult result in results) {
          final newState = _getConnectivityStatus(result);

          if (newState == state) return;

          state = newState;
        }
      },
    );

    _streamSubscription.onError(
      (e) {
        printInDebug('CONN CHECK ERR::: ${e.toString()}');

        return;
      },
    );
  }

  ConnectivityStatus _getConnectivityStatus(ConnectivityResult result) {
    if (result == ConnectivityResult.wifi || result == ConnectivityResult.mobile) {
      return ConnectivityStatus.connected;
    }

    return ConnectivityStatus.disconnected;
  }

  @override
  void dispose() {
    _streamSubscription.cancel();

    return super.dispose();
  }
}
