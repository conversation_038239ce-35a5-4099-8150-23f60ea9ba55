import 'dart:io';

import 'package:dio/dio.dart';

import '../../config/constants/api_constant.dart';

class Failure {
  Failure({
    required this.error,
    this.statusCode,
  });

  final String error;
  final int? statusCode;

  factory Failure.fromDioException(DioException err) {
    return Failure(
      error: _mapErr(err),
      statusCode: err.response?.statusCode ?? 0,
    );
  }

  static String _mapErr(DioException err) {
    switch (err.type) {
      case DioExceptionType.connectionTimeout:
        return ApiConstant.kConnectionTimeout;

      case DioExceptionType.sendTimeout:
        return ApiConstant.kSendTimeout;

      case DioExceptionType.receiveTimeout:
        return ApiConstant.kReceiveTimeout;

      case DioExceptionType.cancel:
        return ApiConstant.kCancel;

      case DioExceptionType.connectionError:
        return err.error is SocketException
            ? ApiConstant.kSocket
            : '${ApiConstant.kConnection}: ${err.error.runtimeType}';

      case DioExceptionType.badResponse:
        return _handleBadResponse(err);

      case DioExceptionType.unknown:
        return '${ApiConstant.kUnexpected}: ${err.message}';

      default:
        return ApiConstant.kUnknown;
    }
  }

  static String _handleBadResponse(DioException err) {
    final statusCode = err.response?.statusCode;

    final String? errMssg = err.response?.data;

    switch (statusCode) {
      case 400:
        if (errMssg == null || errMssg.isEmpty) return ApiConstant.k400;

        return errMssg;

      case 401:
        if (errMssg == null || errMssg.isEmpty) return ApiConstant.k401;

        return errMssg;

      case 403:
        if (errMssg == null || errMssg.isEmpty) return ApiConstant.k403;

        return errMssg;

      case 404:
        if (errMssg == null || errMssg.isEmpty) return ApiConstant.k404;

        return errMssg;

      case 408:
        if (errMssg == null || errMssg.isEmpty) return ApiConstant.k408;

        return errMssg;

      case 429:
        if (errMssg == null || errMssg.isEmpty) return ApiConstant.k429;

        return errMssg;

      case 500:
        if (errMssg == null || errMssg.isEmpty) return ApiConstant.k500;

        return errMssg;

      case 503:
        if (errMssg == null || errMssg.isEmpty) return ApiConstant.k503;

        return errMssg;

      default:
        return errMssg == null || errMssg.isEmpty
            ? 'Error $statusCode: ${err.response?.statusMessage ?? 'Unknown server error.'}'
            : errMssg;
    }
  }

  @override
  String toString() {
    return 'Failure(error: $error, statusCode: $statusCode)';
  }
}
