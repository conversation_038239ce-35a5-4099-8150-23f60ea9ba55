import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

extension StringExtensions on String {
  SvgPicture toSvg({double? h, double? w, Color? color}) {
    return SvgPicture.asset(
      this,
      height: h,
      width: w,
      colorFilter: color != null ? ColorFilter.mode(color, BlendMode.srcIn) : null,
    );
  }

  Image toImg({double? h, double? w}) {
    return Image.asset(this, height: h, width: w);
  }
}
