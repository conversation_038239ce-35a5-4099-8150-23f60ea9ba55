import '../../import/ui.dart';

class TextWidget extends StatelessWidget {
  const TextWidget({
    super.key,
    required this.text,
    this.overflow = TextOverflow.ellipsis,
    this.maxLine,
    this.style,
  });

  final String text;
  final TextOverflow? overflow;
  final int? maxLine;
  final TextStyle? style;

  @override
  Widget build(BuildContext context) {
    return Text(
      text,
      overflow: overflow,
      maxLines: maxLine,
      style: style,
    );
  }
}
