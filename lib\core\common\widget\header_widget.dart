import '../../import/ui.dart';
import 'circle_svg_widget.dart';

class HeaderWidget extends StatelessWidget {
  const HeaderWidget({
    super.key,
    required this.title,
  });

  final String title;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.w),
          child: SizedBox(
            width: double.infinity,
            height: 0.05.sh,
            child: Stack(
              alignment: Alignment.center,
              children: [
                Positioned(
                  left: 0,
                  child: GestureDetector(
                    onTap: () async {
                      context.popTimes(1);
                    },
                    child: CircleSvgWidget(
                      padding: const EdgeInsets.all(15),
                      svg: Assets.svg.backArrow.svg(),
                    ),
                  ),
                ),
                TextWidget(
                  text: title,
                  style: context.tL,
                ),
              ],
            ),
          ),
        ),
        20.verticalSpace,
        const Divider(),
        20.verticalSpace,
      ],
    );
  }
}
