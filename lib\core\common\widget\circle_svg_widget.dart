import 'package:flutter_svg/svg.dart';

import '../../import/ui.dart';

class CircleSvgWidget extends StatelessWidget {
  const CircleSvgWidget({
    super.key,
    this.circleColor = ColorConstant.bg,
    this.withBorder = true,
    this.padding = const EdgeInsets.all(10),
    required this.svg,
  });

  final Color circleColor;
  final bool withBorder;
  final EdgeInsetsGeometry padding;
  final SvgPicture svg;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: circleColor,
        border: !withBorder
            ? null
            : Border.all(
                color: ColorConstant.gray400,
                width: 1.w,
              ),
        shape: BoxShape.circle,
      ),
      padding: padding,
      child: svg,
    );
  }
}
