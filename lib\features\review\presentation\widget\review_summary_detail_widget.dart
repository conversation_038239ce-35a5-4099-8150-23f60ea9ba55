import '../../../../core/common/widget/pfp_widget.dart';
import '../../../../core/import/ui.dart';
import '../view/review_screen.dart';

class ReviewSummaryDetailWidget extends StatelessWidget {
  const ReviewSummaryDetailWidget({
    super.key,
    required this.review,
  });

  final Review review;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 0.6.sw,
      decoration: BoxDecoration(
        color: ColorConstant.white,
        borderRadius: BorderRadius.circular(15.r),
        border: Border.all(
          color: ColorConstant.gray400,
          width: 1.w,
        ),
      ),
      padding: const EdgeInsets.all(10),
      margin: EdgeInsets.only(right: 18.w),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Row(
                  children: [
                    PfpWidget(
                      radius: 15.r,
                      imgPath: Assets.img.catPfp.path,
                    ),
                    5.horizontalSpace,
                    Expanded(
                      child: TextWidget(
                        text: review.name,
                        style: context.tS,
                      ),
                    ),
                  ],
                ),
              ),
              Row(
                children: [
                  TextWidget(
                    text: review.rating.toString(),
                    style: context.tS,
                  ),
                  5.horizontalSpace,
                  Assets.svg.star.svg(),
                ],
              ),
            ],
          ),
          if (review.review != null && review.review!.isNotEmpty) ...[
            10.verticalSpace,
            TextWidget(
              text: review.review!,
              style: context.bS,
              maxLine: 2,
            ),
          ],
        ],
      ),
    );
  }
}
