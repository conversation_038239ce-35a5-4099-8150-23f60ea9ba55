import '../../../../core/import/ui.dart';
import '../../data/model/request/register_req_model.dart';
import '../../domain/usecase/auth_use_case.dart';
import '../state/auth_state.dart';

final authViewModelProvider = StateNotifierProvider<AuthViewModel, AuthState>(
  (ref) => AuthViewModel(ref.read(authUseCaseProvider)),
);

class AuthViewModel extends StateNotifier<AuthState> {
  AuthViewModel(this._authUseCase) : super(AuthState.initial());

  final AuthUseCase _authUseCase;

  void togglePWObscurity() {
    state = state.copyWith(isPwObscure: !state.isPwObscure);
  }

  void updateName(String name) {
    state = state.copyWith(name: name);
  }

  void updateUsername(String username) {
    state = state.copyWith(username: username);
  }

  void updateEmail(String email) {
    state = state.copyWith(email: email);
  }

  void updatePhoneNum(String phoneNum) {
    state = state.copyWith(phoneNumber: phoneNum);
  }

  void updatePW(String pw) {
    state = state.copyWith(password: pw);
  }

  Future<void> login(BuildContext context, String username, String pw) async {
    state = state.copyWith(isLoading: true);

    final data = await _authUseCase.login(username, pw);

    data.fold(
      (failure) {
        state = state.copyWith(isLoading: false, error: failure.error);
      },
      (success) async {
        state = state.copyWith(isLoading: false, authEntity: success);

        await Navigator.pushNamedAndRemoveUntil(
          context,
          AppRoute.homeRoute,
          (_) => false,
        );
      },
    );
  }

  Future<void> register(BuildContext context, RegisterReqModel regReqModel) async {
    state = state.copyWith(isLoading: true);

    final data = await _authUseCase.register(regReqModel);

    data.fold(
      (failure) {
        state = state.copyWith(isLoading: false, error: failure.error);
      },
      (success) async {
        state = state.copyWith(isLoading: false, authEntity: success);

        await Navigator.pushNamedAndRemoveUntil(
          context,
          AppRoute.homeRoute,
          (_) => false,
        );
      },
    );
  }
}
