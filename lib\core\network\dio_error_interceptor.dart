import 'package:dio/dio.dart';

import '../failure/failure.dart';

class DioErrorInterceptor extends Interceptor {
  final Map<String?, String> _errCache = {};

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    if (_errCache.containsKey(err.message)) {
      return super.onError(err, handler);
    }

    final failure = Failure.fromDioException(err);
    _errCache[err.message] = failure.error;

    err = _rebuildDioException(err, failure);

    return super.onError(err, handler);
  }

  DioException _rebuildDioException(DioException err, Failure failure) {
    return DioException(
      requestOptions: err.requestOptions,
      response: err.response,
      error: failure.error,
      type: err.type,
    );
  }
}
