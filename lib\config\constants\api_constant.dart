class ApiConstant {
  ApiConstant._();

  // no internet
  static const kNoInternet = 'Please check your internet connection and try again';

  // request errors
  static const kConnectionTimeout = 'Connection timed out. Please try again';
  static const kSendTimeout = 'Request timed out. Please try again';
  static const kReceiveTimeout = 'Server took too long to respond';
  static const kCancel = 'Request was cancelled';
  static const kConnection = 'Connection error';
  static const kSocket = 'No Internet connection, please check your network';
  static const kUnexpected = 'Unexpected error occurred';
  static const kUnknown = 'An unknown error occurred';

  // response errors
  static const k400 = 'Bad Request: Invalid syntax.';
  static const k401 = 'Unauthorized: Invalid credentials.';
  static const k403 = 'Forbidden: Access denied.';
  static const k404 = 'Not Found: Resource not found.';
  static const k408 = 'Request Timeout: Server took too long';
  static const k429 = 'Too Many Requests: Try again later';
  static const k500 = 'Internal Server Error: Please try again later.';
  static const k503 = 'Service Unavailable: Server is under maintenance.';
}
