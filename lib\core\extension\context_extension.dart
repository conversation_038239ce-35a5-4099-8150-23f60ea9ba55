import '../import/ui.dart';

extension ContextExtension on BuildContext {
  void popTimes(int count) {
    if (count <= 0) return;

    final navigator = Navigator.of(this);

    while (count-- > 0 && navigator.canPop()) {
      navigator.pop();
    }
  }

  TextStyle get dL => Theme.of(this).textTheme.displayLarge!;
  TextStyle get dM => Theme.of(this).textTheme.displayMedium!;
  TextStyle get dS => Theme.of(this).textTheme.displaySmall!;

  TextStyle get hL => Theme.of(this).textTheme.headlineLarge!;
  TextStyle get hM => Theme.of(this).textTheme.headlineMedium!;
  TextStyle get hS => Theme.of(this).textTheme.headlineSmall!;

  TextStyle get tL => Theme.of(this).textTheme.titleLarge!;
  TextStyle get tM => Theme.of(this).textTheme.titleMedium!;
  TextStyle get tS => Theme.of(this).textTheme.titleSmall!;

  TextStyle get bL => Theme.of(this).textTheme.bodyLarge!;
  TextStyle get bM => Theme.of(this).textTheme.bodyMedium!;
  TextStyle get bS => Theme.of(this).textTheme.bodySmall!;

  TextStyle get lL => Theme.of(this).textTheme.labelLarge!;
  TextStyle get lM => Theme.of(this).textTheme.labelMedium!;
  TextStyle get lS => Theme.of(this).textTheme.labelSmall!;
}
