import '../../../../core/import/ui.dart';
import '../view/review_screen.dart';
import 'review_detail_widget.dart';

class ReviewListWidget extends StatelessWidget {
  const ReviewListWidget({
    super.key,
    required this.reviews,
  });

  final List<Review> reviews;

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: EdgeInsets.zero,
      itemCount: reviews.length,
      itemBuilder: (context, i) {
        final review = reviews[i];

        return ReviewDetailWidget(review: review);
      },
    );
  }
}
