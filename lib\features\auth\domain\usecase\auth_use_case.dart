import '../../../../core/import/ui.dart';
import '../../../../core/utils/typedef.dart';
import '../../data/model/request/register_req_model.dart';
import '../entity/auth_entity.dart';
import '../repository/auth_repository.dart';

final authUseCaseProvider = Provider(
  (ref) => AuthUseCase(ref.read(authRepositoryProvider)),
);

class AuthUseCase {
  AuthUseCase(this._authRepository);

  final IAuthRepository _authRepository;

  REQUESTHANDLER<AuthEntity> login(String username, String pw) async {
    return await _authRepository.login(username, pw);
  }

  REQUESTHANDLER<AuthEntity> register(RegisterReqModel regReqModel) async {
    return await _authRepository.register(regReqModel);
  }
}
