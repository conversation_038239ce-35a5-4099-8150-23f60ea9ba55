include: package:flutter_lints/flutter.yaml

# analyzer:
#   errors:
#     avoid_print: warning
#     prefer_single_quotes: warning

linter:
  rules:
    - avoid_print
    - prefer_single_quotes
    - avoid_slow_async_io
    - cancel_subscriptions
    - no_self_assignments
    - prefer_relative_imports
    - use_build_context_synchronously
    - avoid_escaping_inner_quotes
    - avoid_final_parameters
    - avoid_multiple_declarations_per_line
    - avoid_returning_null_for_void
    - avoid_types_on_closure_parameters
    - avoid_unnecessary_containers
    - avoid_void_async
    - await_only_futures
    - camel_case_extensions
    - camel_case_types
    - cast_nullable_to_non_nullable
    - empty_catches
    - file_names
    - join_return_with_assignment
    - sized_box_shrink_expand
    - use_colored_box
    - use_decorated_box
