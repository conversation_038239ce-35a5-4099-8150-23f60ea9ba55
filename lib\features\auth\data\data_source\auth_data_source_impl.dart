import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';

import '../../../../config/constants/api_endpoint.dart';
import '../../../../core/import/ui.dart';
import '../../../../core/network/http_service.dart';
import '../../../../core/network/remote_request_handler.dart';
import '../../../../core/storage/secure_storage.dart';
import '../../../../core/utils/typedef.dart';
import '../model/auth_model.dart';
import '../model/request/register_req_model.dart';
import 'auth_data_source.dart';

final authRemoteDataSourceProvider = Provider<AuthDataSourceIMPL>(
  (ref) => AuthDataSourceIMPL(
    dio: ref.read(httpServiceProvider),
    secureStorage: ref.read(secureStorageProvider),
  ),
);

class AuthDataSourceIMPL implements IAuthDataSource {
  AuthDataSourceIMPL({
    required this.dio,
    required this.secureStorage,
  });

  final Dio dio;
  final SecureStorage secureStorage;

  @override
  REQUESTHANDLER<AuthModel> login(String username, String pw) async {
    return handleRequest(
      request: () async {
        return await dio.post(
          ApiEndpoint.login,
          data: {'username': username, 'password': pw},
        );
      },
      onSuccess: (response) async {
        await secureStorage.writeToken('token', response.data['token']);

        final authModel = AuthModel.fromJson(response.data);

        return Right(authModel);
      },
    );
  }

  @override
  REQUESTHANDLER<AuthModel> register(RegisterReqModel regReqModel) async {
    return handleRequest(
      request: () async {
        return dio.post(
          ApiEndpoint.register,
          data: regReqModel.toJson(),
        );
      },
      onSuccess: (response) async {
        await secureStorage.writeToken('token', response.data['token']);

        final authModel = AuthModel.fromJson(response.data);

        return Right(authModel);
      },
    );
  }
}
