name: gaadiyo
description: "A new Flutter project."

publish_to: "none"

version: 1.0.0+1

environment:
  sdk: ^3.5.4

dependencies:
  flutter:
    sdk: flutter

  cupertino_icons: ^1.0.8
  flutter_riverpod: ^2.6.1
  dio: ^5.8.0+1
  pretty_dio_logger: ^1.4.0
  dartz: ^0.10.1
  flutter_gen: ^5.8.0
  freezed_annotation: ^2.4.4
  flutter_svg: ^2.0.17
  connectivity_plus: ^6.1.2
  flutter_screenutil: ^5.9.3
  flutter_secure_storage: ^4.2.1
  jwt_decoder: ^2.0.1
  json_serializable: ^6.9.4
  json_annotation: ^4.9.0
  google_fonts: ^6.2.1

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^4.0.0
  flutter_gen_runner: ^5.8.0
  build_runner: ^2.4.14
  freezed: ^2.5.7

flutter:
  uses-material-design: true

  assets:
    - assets/img/
    - assets/svg/
    - assets/font/

  fonts:
    - family: Satoshi
      fonts:
        - asset: assets/font/satoshi/Satoshi-Black.otf
          weight: 900
        - asset: assets/font/satoshi/Satoshi-BlackItalic.otf
          weight: 900
          style: italic

        - asset: assets/font/satoshi/Satoshi-Bold.otf
          weight: 700
        - asset: assets/font/satoshi/Satoshi-BoldItalic.otf
          weight: 700
          style: italic

        - asset: assets/font/satoshi/Satoshi-Medium.otf
          weight: 500
        - asset: assets/font/satoshi/Satoshi-MediumItalic.otf
          weight: 500
          style: italic

        - asset: assets/font/satoshi/Satoshi-Regular.otf
          weight: 400
        - asset: assets/font/satoshi/Satoshi-Italic.otf
          weight: 400
          style: italic

        - asset: assets/font/satoshi/Satoshi-Light.otf
          weight: 300
        - asset: assets/font/satoshi/Satoshi-LightItalic.otf
          weight: 300
          style: italic

flutter_gen:
  assets:
    enabled: true
    # outputs:
    #   package_parameter_enabled: true
  output: lib/generated/
  line_length: 80

  integrations:
    flutter_svg: true
