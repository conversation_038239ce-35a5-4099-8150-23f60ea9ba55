import '../../../../core/import/ui.dart';
import '../widget/login_buttons_widget.dart';
import '../widget/login_text_fields_widget.dart';

class LoginScreen extends StatelessWidget {
  const LoginScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final formKey = GlobalKey<FormState>();

    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Scaffold(
        body: SafeArea(
          bottom: false,
          child: Form(
            key: formKey,
            child: SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.only(left: 20.w, right: 20.w, bottom: 20.h),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Assets.svg.login.svg(height: 0.3.sh),
                    TextWidget(
                      text: 'Login',
                      style: context.hL,
                    ),
                    30.verticalSpace,
                    const LoginTextFieldsWidget(),
                    20.verticalSpace,
                    GestureDetector(
                      onTap: () async => await Navigator.pushNamed(context, AppRoute.forgotPasswordRoute),
                      child: Align(
                        alignment: Alignment.centerRight,
                        child: TextWidget(
                          text: 'Forgot Password?',
                          style: context.bM.copyWith(
                            fontWeight: FontWeight.bold,
                            color: ColorConstant.secondary,
                          ),
                        ),
                      ),
                    ),
                    20.verticalSpace,
                    LoginButtonsWidget(
                      formKey: formKey,
                    ),
                    50.verticalSpace,
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        TextWidget(
                          text: 'New to Gaadiyo?',
                          style: context.bM,
                        ),
                        5.horizontalSpace,
                        GestureDetector(
                          onTap: () async => await Navigator.pushNamed(context, AppRoute.registerRoute),
                          child: TextWidget(
                            text: 'Register',
                            style: context.bM.copyWith(
                              color: ColorConstant.secondary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
