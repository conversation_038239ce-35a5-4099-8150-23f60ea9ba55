import '../../../../core/common/widget/circle_svg_widget.dart';
import '../../../../core/import/ui.dart';

class HomeSearchAndFilterWidget extends StatelessWidget {
  const HomeSearchAndFilterWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      child: Row(
        children: [
          Expanded(
            child: TextFormFieldWidget(
              hintText: 'Search . . .',
              hintStyle: context.lS.copyWith(color: ColorConstant.gray400),
              prefixIcon: Padding(
                padding: EdgeInsets.only(
                  left: 20.w,
                  right: 10.w,
                  top: 16.h,
                  bottom: 16.h,
                ),
                child: Assets.svg.search.svg(
                  colorFilter: const ColorFilter.mode(
                    ColorConstant.gray600,
                    BlendMode.srcIn,
                  ),
                ),
              ),
            ),
          ),
          26.horizontalSpace,
          CircleSvgWidget(
            circleColor: ColorConstant.white,
            padding: const EdgeInsets.all(14),
            svg: Assets.svg.filter.svg(
              colorFilter: const ColorFilter.mode(
                ColorConstant.primary,
                BlendMode.srcIn,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
