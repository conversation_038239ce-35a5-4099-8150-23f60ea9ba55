import '../../../../core/common/widget/header_widget.dart';
import '../../../../core/import/ui.dart';
import '../../../home/<USER>/widget/home_body_widget.dart';
import '../../../review/presentation/view/review_screen.dart';
import '../widget/vehicle_detail_body_widget.dart';
import '../widget/vehicle_detail_image_widget.dart';

class VehicleDetailScreen extends StatelessWidget {
  const VehicleDetailScreen({
    super.key,
    required this.vehicle,
  });

  final Vehicle vehicle;

  @override
  Widget build(BuildContext context) {
    final reviews = <Review>[
      Review(
        pfpPath: Assets.img.catPfp.path,
        name: '<PERSON> 1',
        daysAgo: 0,
        rating: 5.0,
        review:
            'The rental car was clean, reliable, and the service was quick and efficient. Overall, the experience was hassle-free and enjoyable.',
      ),
      Review(
        pfpPath: Assets.img.catPfp.path,
        name: '<PERSON> 2',
        daysAgo: 1,
        rating: 4.0,
        review:
            'The rental car was clean, reliable, and the service was quick and efficient. Overall, the experience was hassle-free and enjoyable.',
      ),
      Review(
        pfpPath: Assets.img.catPfp.path,
        name: 'John Doe 3',
        daysAgo: 7,
        rating: 3.0,
        review:
            'The rental car was clean, reliable, and the service was quick and efficient. Overall, the experience was hassle-free and enjoyable.',
      ),
      Review(
        pfpPath: Assets.img.catPfp.path,
        name: 'John Doe 4',
        daysAgo: 30,
        rating: 2.0,
        review:
            'The rental car was clean, reliable, and the service was quick and efficient. Overall, the experience was hassle-free and enjoyable.',
      ),
      Review(
        pfpPath: Assets.img.catPfp.path,
        name: 'John Doe 5',
        daysAgo: 365,
        rating: 1.0,
      ),
      Review(
        pfpPath: Assets.img.catPfp.path,
        name: 'John Doe 6',
        daysAgo: 730,
        rating: 1.0,
      ),
    ];

    return Scaffold(
      body: SafeArea(
        bottom: false,
        child: SingleChildScrollView(
          child: Column(
            children: [
              const HeaderWidget(title: 'Vehicle Details'),
              Hero(
                tag: vehicle.name,
                child: const VehicleDetailImageWidget(),
              ),
              20.verticalSpace,
              VehicleDetailBodyWidget(vehicle: vehicle, reviews: reviews),
            ],
          ),
        ),
      ),
    );
  }
}
