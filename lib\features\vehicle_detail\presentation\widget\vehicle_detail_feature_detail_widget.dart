import 'package:flutter_svg/flutter_svg.dart';

import '../../../../core/common/widget/circle_svg_widget.dart';
import '../../../../core/import/ui.dart';
import 'vehicle_detail_feature_list_widget.dart';

class VehicleDetailFeatureDetailWidget extends StatelessWidget {
  const VehicleDetailFeatureDetailWidget({
    super.key,
    required this.feature,
  });

  final Feature feature;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: ColorConstant.gray200,
        borderRadius: BorderRadius.circular(10.r),
      ),
      padding: const EdgeInsets.all(10),
      margin: EdgeInsets.only(bottom: 18.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CircleSvgWidget(
            circleColor: ColorConstant.white,
            withBorder: false,
            svg: feature.img as SvgPicture,
          ),
          18.verticalSpace,
          TextWidget(
            text: feature.title,
            style: context.bS.copyWith(
              color: ColorConstant.gray600,
              fontWeight: FontWeight.w500,
            ),
          ),
          5.verticalSpace,
          TextWidget(
            text: feature.description,
            style: context.bM.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}
