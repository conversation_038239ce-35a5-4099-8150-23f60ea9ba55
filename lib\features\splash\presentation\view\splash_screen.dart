import '../../../../core/import/ui.dart';
import '../viewmodel/splash_view_model.dart';

class SplashScreen extends ConsumerStatefulWidget {
  const SplashScreen({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _SplashScreenState();
}

class _SplashScreenState extends ConsumerState<SplashScreen> {
  @override
  void initState() {
    super.initState();

    Future.microtask(() => init());
  }

  Future<void> init() async {
    await ref.read(splashViewModelProvider.notifier).init(context);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Center(
            child: Assets.svg.login.svg(height: 80.h),
          ),
          20.verticalSpace,
          Consumer(
            builder: (context, ref, child) {
              final isLoading = ref.watch(
                splashViewModelProvider.select((state) => state.isLoading),
              );

              return isLoading ? CircularProgressIndicator(strokeWidth: 4.w) : const SizedBox.shrink();
            },
          ),
        ],
      ),
    );
  }
}
