import '../../import/ui.dart';

class ButtonWidget extends StatelessWidget {
  const ButtonWidget({
    super.key,
    this.buttonHeight,
    this.buttonWidth = double.infinity,
    this.onPressed,
    this.isLoading = false,
    required this.text,
    this.textStyle,
    this.buttonColor = ColorConstant.primary,
  });

  final double? buttonHeight;
  final double buttonWidth;
  final VoidCallback? onPressed;
  final bool isLoading;
  final String text;
  final TextStyle? textStyle;
  final Color buttonColor;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: buttonHeight,
      width: buttonWidth,
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: buttonColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(62.r),
          ),
          overlayColor: ColorConstant.transparent,
        ),
        child: isLoading
            ? SizedBox(
                height: 20.h,
                width: 20.w,
                child: CircularProgressIndicator(strokeWidth: 2.w),
              )
            : TextWidget(
                text: text,
                style: textStyle ?? context.lM.copyWith(color: ColorConstant.bg),
                maxLine: 2,
              ),
      ),
    );
  }
}
