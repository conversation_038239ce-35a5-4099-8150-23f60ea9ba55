import '../../../../core/import/ui.dart';
import '../../../review/presentation/view/review_screen.dart';
import '../../../review/presentation/widget/review_summary_list_widget.dart';

class VehicleDetailBodyReviewWidget extends StatelessWidget {
  const VehicleDetailBodyReviewWidget({
    super.key,
    required this.reviews,
  });

  final List<Review> reviews;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                TextWidget(
                  text: 'Review',
                  style: context.tM,
                ),
                if (reviews.isNotEmpty)
                  TextWidget(
                    text: ' (${reviews.length})',
                    style: context.tM,
                  ),
              ],
            ),
            GestureDetector(
              onTap: reviews.isEmpty
                  ? null
                  : () async {
                      await Navigator.pushNamed(
                        context,
                        AppRoute.reviewRoute,
                        arguments: reviews,
                      );
                    },
              child: TextWidget(
                text: 'View All',
                style: context.bS.copyWith(
                  color: reviews.isEmpty ? ColorConstant.gray400 : ColorConstant.secondary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        25.verticalSpace,
        ReviewSummaryListWidget(reviews: reviews),
      ],
    );
  }
}
