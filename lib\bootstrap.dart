import 'dart:async';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'startup.dart';

class AppProviderObserver extends ProviderObserver {
  AppProviderObserver._();

  static final instance = AppProviderObserver._();

  @override
  void didAddProvider(ProviderBase provider, Object? value, ProviderContainer container) {
    super.didAddProvider(provider, value, container);

    log('''
{
  "event": "add",
  "provider": "${provider.name ?? provider.runtimeType}",
  "initialValue": "$value"
}''');
  }

  @override
  void providerDidFail(
    ProviderBase<Object?> provider,
    Object error,
    StackTrace stackTrace,
    ProviderContainer container,
  ) {
    super.providerDidFail(provider, error, stackTrace, container);

    log('''
{
  "event": "error",
  "provider": "${provider.name ?? provider.runtimeType}",
  "error": "$error",
  "stackTrace": "$stackTrace"
}''');
  }

  @override
  void didUpdateProvider(
    ProviderBase provider,
    Object? previousValue,
    Object? newValue,
    ProviderContainer container,
  ) {
    super.didUpdateProvider(provider, previousValue, newValue, container);

    log('''
{
  "event": "update",
  "provider": "${provider.name ?? provider.runtimeType}",
  "previousValue": "$previousValue",
  "newValue": "$newValue"
}''');
  }

  @override
  void didDisposeProvider(ProviderBase provider, ProviderContainer container) {
    super.didDisposeProvider(provider, container);

    log('''
{
  "event": "dispose",
  "provider": "${provider.name ?? provider.runtimeType}",
}''');
  }
}

Future<void> bootstrap(Widget app) async {
  runZonedGuarded(
    () async {
      await initializeApp();

      runApp(
        ProviderScope(
          observers: [AppProviderObserver.instance],
          child: app,
        ),
      );
    },
    (error, stackTrace) {
      log('Zone caught err: $error', stackTrace: stackTrace);
    },
  );
}
