import '../../../../core/common/widget/button_widget.dart';
import '../../../../core/import/ui.dart';
import '../viewmodel/auth_view_model.dart';

class LoginButtonsWidget extends StatelessWidget {
  const LoginButtonsWidget({
    super.key,
    required this.formKey,
  });

  final GlobalKey<FormState> formKey;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Consumer(
          builder: (context, ref, child) {
            final isLoading = ref.watch(
              authViewModelProvider.select((state) => state.isLoading),
            );

            return ButtonWidget(
              onPressed: () async {
                if (!formKey.currentState!.validate()) return;

                await ref.read(authViewModelProvider.notifier).login(
                      context,
                      ref.read(authViewModelProvider).email ?? '',
                      ref.read(authViewModelProvider).password ?? '',
                    );
              },
              text: 'Login',
              isLoading: isLoading,
              buttonHeight: 50.h,
            );
          },
        ),
        25.verticalSpace,
        Row(
          children: [
            const Expanded(
              child: Divider(),
            ),
            20.horizontalSpace,
            const TextWidget(text: 'Or'),
            20.horizontalSpace,
            const Expanded(
              child: Divider(),
            ),
          ],
        ),
        25.verticalSpace,
        ButtonWidget(
          onPressed: () {},
          text: 'Login with Google',
          textStyle: context.lM.copyWith(color: ColorConstant.black),
          buttonHeight: 50.h,
          buttonColor: ColorConstant.gray300,
        ),
      ],
    );
  }
}
