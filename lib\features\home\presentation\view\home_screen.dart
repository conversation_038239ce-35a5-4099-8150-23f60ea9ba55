import '../../../../core/import/ui.dart';
import '../widget/home_body_widget.dart';
import '../widget/home_categories_widget.dart';
import '../widget/home_header_widget.dart';
import '../widget/home_search_and_filter_widget.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Scaffold(
        body: SafeArea(
          bottom: false,
          child: SingleChildScrollView(
            child: Column(
              children: [
                const HomeHeaderWidget(),
                20.verticalSpace,
                const HomeSearchAndFilterWidget(),
                28.verticalSpace,
                const HomeCategoriesWidget(),
                const HomeBodyWidget(),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
