import '../../../../core/import/ui.dart';
import '../../../../core/utils/typedef.dart';
import '../../data/model/request/register_req_model.dart';
import '../../data/repository/auth_repo_impl.dart';
import '../entity/auth_entity.dart';

final authRepositoryProvider = Provider(
  (ref) => ref.read(authRepoIMPLProvider),
);

abstract class IAuthRepository {
  REQUESTHANDLER<AuthEntity> login(String username, String pw);

  REQUESTHANDLER<AuthEntity> register(RegisterReqModel regReqModel);
}
