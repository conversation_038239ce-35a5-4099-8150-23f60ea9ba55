import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

Future<void> initializeApp() async {
  WidgetsFlutterBinding.ensureInitialized();

  await SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);

  await SystemChannels.textInput.invokeMethod('TextInput.hide');

  FlutterError.onError = (details) {
    FlutterError.dumpErrorToConsole(details);
    log(details.exceptionAsString(), stackTrace: details.stack);
  };
}
