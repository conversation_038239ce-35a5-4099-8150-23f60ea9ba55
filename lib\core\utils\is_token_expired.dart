import 'package:jwt_decoder/jwt_decoder.dart';

import 'print_in_debug.dart';

bool isTokenExpired(String? token) {
  if (token == null) {
    // If the token is null, it is considered expired
    return true;
  }

  try {
    Map<String, dynamic> decodedToken = JwtDecoder.decode(token);
    int? expirationTimestamp = decodedToken['exp'] as int?;

    if (expirationTimestamp == null) {
      // If the expirationTimestamp is null, it is considered expired
      return true;
    }

    final currentDate = DateTime.now().millisecondsSinceEpoch;
    return currentDate > expirationTimestamp * 1000;
  } catch (e) {
    // If an error arises, it is considered expired
    printInDebug('ERROR DECODING OR VALIDATING TOKEN::: $e');
    return true;
  }
}
