extension IntExtension on int {
  String get timeAgo {
    if (isNegative || isInfinite || isNaN) return '';

    if (this <= 0) return 'Today';

    if (this == 1) return 'Yesterday';

    if (this < 7) return "$this day's ago";

    if (this < 14) return 'A week ago';
    if (this < 30) return "${(this / 7).floor()} week's ago";

    if (this < 60) return 'A month ago';
    if (this < 365) return "${(this / 30).floor()} month's ago";

    if (this < 730) return 'A year ago';

    return 'More than a year ago';
  }
}
