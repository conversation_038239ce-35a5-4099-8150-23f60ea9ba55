import 'package:json_annotation/json_annotation.dart';

import '../../../../core/import/ui.dart';
import '../../domain/entity/auth_entity.dart';

part 'auth_model.g.dart';

final authModelProvider = Provider<AuthModel>((ref) => AuthModel.empty());

@JsonSerializable()
class AuthModel {
  AuthModel({
    required this.message,
    this.name,
    this.username,
    this.email,
    this.phoneNumber,
    this.role,
  });

  final String message;
  final String? name;
  final String? username;
  final String? email;
  final String? phoneNumber;
  final String? role;

  factory AuthModel.fromJson(Map<String, dynamic> json) => _$AuthModelFromJson(json);

  Map<String, dynamic> toJson() => _$AuthModelToJson(this);

  AuthModel.empty()
      : message = '',
        name = null,
        username = null,
        email = null,
        phoneNumber = null,
        role = null;

  AuthEntity toEntity() {
    return AuthEntity(
      name: name,
      username: username,
      email: email,
      phoneNumber: phoneNumber,
      role: role,
    );
  }

  @override
  String toString() {
    return 'AuthModel(message: $message, name: $name, username: $username, email: $email, phoneNumber: $phoneNumber, role: $role)';
  }
}
