import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';

import '../../../../core/failure/failure.dart';
import '../../config/constants/api_constant.dart';
import '../import/ui.dart';
import '../provider/check_connection.dart';
import '../utils/typedef.dart';

REQUESTHANDLER<T> handleRequest<T>({
  required Future<Response> Function() request,
  required REQUESTHANDLER<T> Function(Response) onSuccess,
  WidgetRef? ref,
}) async {
  final connectivityStatus = ref?.read(connectivityStatusProvider);

  if (connectivityStatus != null && connectivityStatus == ConnectivityStatus.disconnected) {
    return Left(
      Failure(error: ApiConstant.kNoInternet),
    );
  }

  try {
    final response = await request();

    if (response.statusCode != null && (response.statusCode! >= 200 && response.statusCode! < 300)) {
      return await onSuccess(response);
    }

    return Left(
      Failure(
        error: response.data['message'],
        statusCode: response.statusCode,
      ),
    );
  } on DioException catch (e) {
    return Left(
      Failure(
        error: e.error.toString(),
        statusCode: e.response?.statusCode,
      ),
    );
  } catch (e) {
    return Left(
      Failure(error: e.toString()),
    );
  }
}
