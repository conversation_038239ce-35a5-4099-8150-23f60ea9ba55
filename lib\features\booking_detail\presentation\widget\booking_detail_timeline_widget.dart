import '../../../../core/import/ui.dart';

class BookingDetailTimelineWidget extends StatelessWidget {
  const BookingDetailTimelineWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.w),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 10.r,
                  backgroundColor: ColorConstant.primary,
                  child: CircleAvatar(
                    radius: 5.r,
                    backgroundColor: ColorConstant.white,
                  ),
                ),
                const Expanded(
                  child: Divider(),
                ),
                CircleAvatar(
                  radius: 10.r,
                  backgroundColor: ColorConstant.primary,
                ),
                const Expanded(
                  child: Divider(),
                ),
                CircleAvatar(
                  radius: 10.r,
                  backgroundColor: ColorConstant.primary,
                ),
              ],
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TextWidget(
                text: 'Booking Details',
                style: context.bS.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              TextWidget(
                text: 'Payment Methods',
                style: context.bS.copyWith(
                  fontWeight: FontWeight.w600,
                  color: ColorConstant.gray500,
                ),
              ),
              TextWidget(
                text: 'Confirmation',
                style: context.bS.copyWith(
                  fontWeight: FontWeight.w600,
                  color: ColorConstant.gray500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
