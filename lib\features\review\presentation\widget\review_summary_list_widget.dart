import '../../../../core/import/ui.dart';
import '../view/review_screen.dart';
import 'review_summary_detail_widget.dart';

class ReviewSummaryListWidget extends StatelessWidget {
  const ReviewSummaryListWidget({
    super.key,
    required this.reviews,
  });

  final List<Review> reviews;

  @override
  Widget build(BuildContext context) {
    if (reviews.isEmpty) {
      return Align(
        child: TextWidget(
          text: 'No Reviews',
          style: context.bM,
        ),
      );
    }

    return SizedBox(
      height: 0.115.sh,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: reviews.length > 3 ? reviews.length - (reviews.length - 3) : reviews.length,
        padding: EdgeInsets.zero,
        itemBuilder: (context, i) {
          final review = reviews[i];

          return ReviewSummaryDetailWidget(review: review);
        },
      ),
    );
  }
}
