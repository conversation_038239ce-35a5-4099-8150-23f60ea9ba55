import '../../../../core/common/widget/button_widget.dart';
import '../../../../core/import/ui.dart';
import '../../../home/<USER>/widget/home_body_widget.dart';
import '../../../review/presentation/view/review_screen.dart';
import 'vehicle_detail_body_review_widget.dart';
import 'vehicle_detail_body_title_widget.dart';
import 'vehicle_detail_feature_list_widget.dart';
import 'vehicle_detail_user_info_widget.dart';

class VehicleDetailBodyWidget extends StatelessWidget {
  const VehicleDetailBodyWidget({
    super.key,
    required this.vehicle,
    required this.reviews,
  });

  final Vehicle vehicle;
  final List<Review> reviews;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: ColorConstant.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(30.r),
          topRight: Radius.circular(30.r),
        ),
      ),
      child: Column(
        children: [
          VehicleDetailBodyTitleWidget(vehicle: vehicle),
          25.verticalSpace,
          const Divider(),
          25.verticalSpace,
          const VehicleDetailUserInfoWidget(),
          25.verticalSpace,
          const VehicleDetailFeaturesWidget(),
          7.verticalSpace,
          VehicleDetailBodyReviewWidget(reviews: reviews),
          25.verticalSpace,
          ButtonWidget(
            onPressed: () async {
              await Navigator.pushNamed(context, AppRoute.bookingDetailRoute);
            },
            text: 'Book Now',
            buttonHeight: 50.h,
            buttonColor: ColorConstant.secondary,
          ),
        ],
      ),
    );
  }
}
