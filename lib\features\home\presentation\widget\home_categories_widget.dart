import 'package:flutter_svg/svg.dart';

import '../../../../core/common/widget/circle_svg_widget.dart';
import '../../../../core/import/ui.dart';

class Category {
  Category({
    required this.icon,
    required this.name,
  });

  final Widget icon;
  final String name;
}

class HomeCategoriesWidget extends StatelessWidget {
  const HomeCategoriesWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final categories = [
      Category(
        icon: Assets.svg.motorcycle.svg(
          height: 30.h,
          width: 30.w,
          colorFilter: const ColorFilter.mode(
            ColorConstant.primary,
            BlendMode.srcIn,
          ),
        ),
        name: '<PERSON>cyle',
      ),
      Category(
        icon: Assets.svg.car.svg(
          height: 30.h,
          width: 30.w,
          colorFilter: const ColorFilter.mode(
            ColorConstant.primary,
            BlendMode.srcIn,
          ),
        ),
        name: '<PERSON>',
      ),
    ];

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextWidget(
            text: 'Categories',
            style: context.tM,
          ),
          18.verticalSpace,
          SizedBox(
            height: 0.145.sh,
            width: double.infinity,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: categories.length,
              padding: EdgeInsets.zero,
              itemBuilder: (listContext, i) {
                final category = categories[i];

                return Padding(
                  padding: i == categories.length - 1 ? EdgeInsets.zero : EdgeInsets.only(right: 20.w),
                  child: Column(
                    children: [
                      CircleSvgWidget(
                        circleColor: ColorConstant.white,
                        padding: const EdgeInsets.all(10),
                        svg: category.icon as SvgPicture,
                      ),
                      18.verticalSpace,
                      TextWidget(
                        text: category.name,
                        style: context.bS.copyWith(fontWeight: FontWeight.w500),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
